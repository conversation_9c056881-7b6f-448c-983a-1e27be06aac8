<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Aria template - tailus</title>
        <meta name="description" content="Modern multi purpose agency template" />

        <!-- Facebook Meta Tags -->
        <meta property="og:url" content="" />
        <meta property="og:type" content="website" />
        <meta property="og:title" content="Aria template" />
        <meta property="og:description" content="Modern multi purpose agency template" />
        <meta property="og:image" content="" />

        <!-- Twitter Meta Tags -->
        <meta name="twitter:card" content="summary_large_image" />
        <meta property="twitter:domain" content="Aria.com" />
        <meta property="twitter:url" content="" />
        <meta name="twitter:title" content="Aria template" />
        <meta name="twitter:description" content="Modern multi purpose agency template" />
        <meta name="twitter:image" content="" />

        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Marcellus&display=swap" rel="stylesheet" />
      <script type="module" crossorigin src="/assets/main2.js"></script>
      <link rel="stylesheet" href="/assets/main.css">
    </head>
    <body class="bg-white dark:bg-stone-950">
        <header class="py-6 sm:py-12 xl:py-24">
            <div class="mx-auto max-w-5xl px-8 sm:px-12 lg:px-8 xl:px-16">
                <div class="flex flex-wrap justify-between lg:items-center">
                    <div class="relative z-30 flex w-full items-center justify-between lg:w-auto">
                        <a href="/" class="relative top-1.5">
                            <img class="h-14 w-auto" src="/favicon.svg" alt="Aria logo" width="120" height="120" />
                        </a>
                        <button aria-label="humburger" id="hamburger" class="-mr-6 p-6 lg:hidden">
                            <div aria-hidden="true" class="m-auto h-0.5 w-6 rounded bg-primary transition duration-300"></div>
                            <div aria-hidden="true" class="m-auto mt-2 h-0.5 w-6 rounded bg-primary transition duration-300"></div>
                        </button>
                    </div>
                    <div id="navlinks" class="fixed inset-0 z-20 hidden h-full w-full items-center justify-center bg-white/90 backdrop-blur-3xl lg:static lg:block lg:h-auto lg:w-auto lg:bg-transparent lg:backdrop-blur-none">
                        <ul class="group flex flex-col items-center gap-6 text-lg text-stone-600 dark:text-stone-300 lg:flex-row lg:gap-0 lg:text-sm">
                            <li class="text-right transition duration-500 group-hover:text-stone-300 lg:text-left">
                                <a aria-current="page" href="/" class="block px-6 py-1 hover:text-primary">Portfolio</a>
                            </li>
                            <li class="text-right transition duration-500 group-hover:text-stone-300 lg:text-left">
                                <a aria-current="page" href="/pages/about.html" class="block px-6 py-1 hover:text-primary">O mně</a>
                            </li>
                            <li class="text-right transition duration-500 group-hover:text-stone-300 lg:text-left">
                                <a href="#" class="block px-6 py-1 hover:text-primary">Instagram</a>
                            </li>
                            <li class="text-right group-hover:text-stone-300 lg:pl-6 lg:text-left">
                                <a href="mailto:<EMAIL>" class="flex h-9 items-center overflow-hidden border border-stone-300 px-3.5 py-1 text-sm transition-all duration-300 hover:border-primary hover:bg-primary hover:text-white">
                                    <span class="relative flex items-center">
                                        <EMAIL>
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-4 w-4 translate-x-1 duration-300 group-hover:translate-x-2.5">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                                        </svg>
                                    </span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </header>
        <main>
            <section class="pt-24 sm:pt-32 lg:pt-20">
                <div data-rellax-speed="-2" class="rellax mx-auto max-w-5xl px-8 sm:px-12 lg:px-8 xl:px-16">
                    <div class="sm:w-4/5">
                        <h1 class="text-3xl font-semibold text-stone-900 dark:text-white sm:text-4xl lg:text-5xl lg:leading-snug">Dovol mi na okamžik zachytit Tvoji duši</h1>
                        <p class="mt-4 text-xl text-stone-600 dark:text-stone-300">Lorem ipsum dolor sit amet consecteur</p>
                    </div>
                </div>
                <div class="relative mt-14 w-full bg-white px-4 pt-12 dark:bg-stone-950 sm:px-12 lg:px-8">
                    <div class="mx-auto grid max-w-7xl gap-4 gap-y-20 sm:grid-cols-5 sm:gap-y-24 md:gap-8 lg:gap-16 lg:gap-y-16">
                        <a href="pages/project.html" class="group sm:col-span-full lg:col-span-3">
                            <div class="relative h-96 overflow-hidden before:absolute before:inset-0 before:z-[1] before:origin-right before:scale-x-0 before:bg-gradient-to-r before:from-transparent before:via-surface/20 before:to-transparent before:transition before:duration-500 group-hover:before:origin-left group-hover:before:scale-x-100 md:h-[30rem] xl:h-[40rem]">
                                <img data-rellax-speed="6" class="rellax h-max w-full" src="/images/work4.webp" alt="project cover" width="1920" height="2821" />
                            </div>
                            <div class="flex items-center justify-between py-4 text-stone-700 dark:text-stone-100">
                                <h2 class="text-lg font-medium duration-300 group-hover:text-primary">Higher Ground School</h2>
                                <span class="text-sm">/ 01</span>
                            </div>
                        </a>
                        <a href="pages/project.html" class="group sm:col-span-4 sm:col-start-2 md:pt-8 lg:col-span-2 lg:pt-16">
                            <div class="relative before:absolute before:inset-0 before:origin-right before:scale-x-0 before:bg-gradient-to-r before:from-transparent before:via-surface/20 before:to-transparent before:transition before:duration-500 group-hover:before:origin-left group-hover:before:scale-x-100">
                                <img class="w-full object-cover" src="/images/work2.webp" alt="project cover" width="1920" height="2688" />
                            </div>
                            <div class="flex items-center justify-between py-4 text-stone-700 dark:text-stone-100">
                                <h2 class="text-lg font-medium duration-300 group-hover:text-primary">Higher Ground School</h2>
                                <span class="text-sm">/ 02</span>
                            </div>
                        </a>
                        <a data-rellax-speed="1.5" data-rellax-xs-speed="0" data-rellax-mobile-speed="0" data-rellax-tablet-speed="0" href="pages/project.html" class="rellax group sm:col-span-3 sm:col-start-3 lg:col-span-2">
                            <div class="relative before:absolute before:inset-0 before:origin-right before:scale-x-0 before:bg-gradient-to-r before:from-transparent before:via-surface/20 before:to-transparent before:transition before:duration-500 group-hover:before:origin-left group-hover:before:scale-x-100">
                                <img class="w-full object-cover" src="/images/work11.webp" alt="project cover" width="1000" height="1500" />
                            </div>
                            <div class="flex items-center justify-between py-4 text-stone-700 dark:text-stone-100">
                                <h2 class="text-lg font-medium duration-300 group-hover:text-primary">Higher Ground School</h2>
                                <span class="text-sm">/ 03</span>
                            </div>
                        </a>
                        <a href="pages/project.html" class="group sm:col-span-4 lg:col-span-3 lg:mt-16">
                            <div class="relative before:absolute before:inset-0 before:origin-right before:scale-x-0 before:bg-gradient-to-r before:from-transparent before:via-surface/20 before:to-transparent before:transition before:duration-500 group-hover:before:origin-left group-hover:before:scale-x-100">
                                <img class="w-full" src="/images/work8.webp" alt="project cover" width="1920" height="2400" />
                            </div>
                            <div class="flex items-center justify-between py-4 text-stone-700 dark:text-stone-100">
                                <h2 class="text-lg font-medium duration-300 group-hover:text-primary">Higher Ground School</h2>
                                <span class="text-sm">/ 04</span>
                            </div>
                        </a>
                        <a href="pages/project.html" class="group gap-6 sm:col-span-full lg:mt-16 lg:flex lg:items-end">
                            <div class="relative before:absolute before:inset-0 before:origin-right before:scale-x-0 before:bg-gradient-to-r before:from-transparent before:via-surface/20 before:to-transparent before:transition before:duration-500 group-hover:before:origin-left group-hover:before:scale-x-100 lg:w-4/5">
                                <img class="w-full" src="/images/work1.webp" alt="project cover" width="1500" height="1000" />
                            </div>
                            <div class="flex justify-between py-4 text-stone-700 dark:text-stone-100 lg:block lg:space-y-4">
                                <span class="text-sm">/ 05</span>
                                <h2 class="order-first text-lg font-medium duration-300 group-hover:text-primary">River Slight</h2>
                            </div>
                        </a>
                    </div>
                </div>
            </section>
            <section class="py-32">
                <div class="mx-auto max-w-7xl px-4 sm:px-12 lg:px-8 xl:px-16">
                    <a href="mailto:<EMAIL>" class="relative mt-4 block text-center text-5xl font-thin sm:text-7xl">
                        <span class="text-stone-700 transition duration-700 hover:bg-gradient-to-r hover:from-accent hover:to-primary hover:bg-clip-text hover:text-transparent dark:text-white"><EMAIL></span>
                    </a>
                    <div class="mx-auto mt-12 sm:w-4/5 md:w-2/5">
                        <p class="text-center text-lg font-normal text-stone-600 dark:text-stone-300">Maecenas libero. Praesent vitae arcu tempor neque lacinia pretium. Phasellus et lorem.</p>
                    </div>
                </div>
            </section>
        </main>
        <footer class="pt-6 dark:bg-stone-900">
            <div class="mx-auto max-w-7xl divide-y divide-white px-4 dark:divide-stone-700 sm:px-12 lg:px-8 xl:px-16">
                <div class="flex flex-col items-center justify-between gap-6 pb-6 sm:flex-row">
                    <a href="/" class="block w-max relative top-1.5">
                        <img class="h-11 w-auto" src="/favicon.svg" alt="Aria logo" width="120" height="120" />
                    </a>
                    <ul class="group items-center justify-center gap-0 text-center text-sm text-stone-200 sm:flex lg:gap-0">
                        <li class="transition duration-500 group-hover:text-stone-300">
                            <a href="#" class="block px-3 py-1">Portfolio</a>
                        </li>
                        <li class="transition duration-500 group-hover:text-stone-300">
                            <a href="pages/about.html" class="block px-3 py-1">O mně</a>
                        </li>
                    </ul>
                </div>
                <div class="flex flex-col flex-wrap items-center justify-between gap-4 py-6 text-sm text-stone-200 sm:flex-row sm:gap-6">
                    <span class="order-last sm:order-first">&copy; 2024 - Yvica Photography</span>
                    <ul class="order-first flex gap-6 sm:order-2">
                        <li>
                            <a href="#" target="_blank" class="transition duration-300 hover:text-stone-400">Instagram</a>
                        </li>
                    </ul>
                    <a href="/pages/privacy.html" class="order-2 transition duration-300 hover:text-stone-400 sm:order-last">Podmínky ochrany osobních údajů</a>
                </div>
            </div>
        </footer>
        
        <script src="https://cdn.jsdelivr.net/gh/dixonandmoe/rellax@master/rellax.min.js"></script>
    </body>
</html>
