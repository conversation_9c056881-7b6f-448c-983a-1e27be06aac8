<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Aria template - tailus</title>
        <meta name="description" content="Modern multi purpose agency template" />

        <!-- Facebook Meta Tags -->
        <meta property="og:url" content="" />
        <meta property="og:type" content="website" />
        <meta property="og:title" content="Aria template" />
        <meta property="og:description" content="Modern multi purpose agency template" />
        <meta property="og:image" content="" />

        <!-- Twitter Meta Tags -->
        <meta name="twitter:card" content="summary_large_image" />
        <meta property="twitter:domain" content="Aria.com" />
        <meta property="twitter:url" content="" />
        <meta name="twitter:title" content="Aria template" />
        <meta name="twitter:description" content="Modern multi purpose agency template" />
        <meta name="twitter:image" content="" />

        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
      <script type="module" crossorigin src="/assets/main2.js"></script>
      <link rel="stylesheet" href="/assets/main.css">
    </head>
    <body class="bg-stone-50 dark:bg-stone-950">
        <header class="py-6 sm:py-12 xl:py-24">
            <div class="mx-auto max-w-5xl px-8 sm:px-12 lg:px-8 xl:px-16">
                <div class="flex flex-wrap justify-between lg:items-center">
                    <div class="relative z-30 flex w-full items-center justify-between overflow-hidden lg:w-auto">
                        <a href="/">
                            <img class="h-12 w-auto" src="/favicon.svg" alt="Aria logo" width="120" height="120" />
                        </a>
                        <button aria-label="humburger" id="hamburger" class="-mr-6 p-6 lg:hidden">
                            <div aria-hidden="true" class="m-auto h-0.5 w-6 rounded bg-primary transition duration-300"></div>
                            <div aria-hidden="true" class="m-auto mt-2 h-0.5 w-6 rounded bg-primary transition duration-300"></div>
                        </button>
                    </div>
                    <div id="navlinks" class="fixed inset-0 z-20 hidden h-full w-full items-center justify-center bg-white/90 backdrop-blur-3xl lg:static lg:block lg:h-auto lg:w-auto lg:bg-transparent lg:backdrop-blur-none">
                        <ul class="group flex flex-col items-center gap-6 text-lg text-stone-600 lg:flex-row lg:gap-0 lg:text-sm">
                            <li class="text-right transition duration-500 group-hover:text-stone-300 lg:text-left">
                                <a aria-current="page" href="/pages/about.html" class="block px-6 py-1 hover:text-primary">About me</a>
                            </li>
                            <li class="text-right transition duration-500 group-hover:text-stone-300 lg:text-left">
                                <a aria-current="page" href="/" class="block px-6 py-1 hover:text-primary">Instagram</a>
                            </li>
                            <li class="text-right transition duration-500 group-hover:text-stone-300 lg:text-left">
                                <a href="/pages/services.html" class="block px-6 py-1 hover:text-primary">Behance</a>
                            </li>
                            <li class="text-right group-hover:text-stone-300 lg:pl-6 lg:text-left">
                                <a href="mailto:<EMAIL>" class="flex h-9 items-center overflow-hidden border border-stone-300 px-3.5 py-1 text-sm transition-all duration-300 hover:border-primary hover:bg-primary hover:text-white">
                                    <span class="relative flex items-center">
                                        <EMAIL>
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-4 w-4 translate-x-1 duration-300 group-hover:translate-x-2.5">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                                        </svg>
                                    </span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </header>
        <main>
            <section class="relative z-10 pt-12 sm:pt-20 md:pt-32 lg:pt-0">
                <div class="mx-auto max-w-7xl px-4 sm:px-12 lg:px-8 xl:px-16">
                    <div class="p-8 pb-20 sm:bg-white md:p-12 lg:p-24">
                        <span class="mx-auto block w-max text-9xl font-thin text-stone-500">404</span>
                        <h1 class="mt-4 text-center text-3xl font-semibold text-primary">Page not found !</h1>
                        <p class="my-6 text-center text-stone-600">
                            This page doesn't exist. <br />
                            Press the button below to go back home
                        </p>
                        <a href="/pages/contact.html" class="group mx-auto flex h-9 w-max items-center overflow-hidden rounded-md border border-stone-300 px-4 py-1 text-sm transition-all duration-300 hover:rounded-lg hover:border-primary hover:bg-primary hover:text-white">
                            <span class="relative flex items-center">
                                Back home
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-4 w-4 translate-x-1 duration-300 group-hover:translate-x-2.5">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                                </svg>
                            </span>
                        </a>
                    </div>
                </div>
            </section>
            <section class="py-32">
                <div class="mx-auto max-w-7xl px-4 sm:px-12 lg:px-8 xl:px-16">
                    <a href="mailto:<EMAIL>" class="relative mt-4 block text-center text-5xl font-thin sm:text-7xl">
                        <span class="text-stone-700 transition duration-700 hover:bg-gradient-to-r hover:from-accent hover:to-primary hover:bg-clip-text hover:text-transparent dark:text-white"><EMAIL></span>
                    </a>
                    <div class="mx-auto mt-12 sm:w-4/5 md:w-2/5">
                        <p class="text-center text-lg font-normal text-stone-600 dark:text-stone-300">I'd love to hear more about your project, or you can just write me "Hello!".</p>
                    </div>
                </div>
            </section>
        </main>
        <footer class="bg-stone-800 pt-6">
            <div class="mx-auto max-w-7xl divide-y divide-white px-4 sm:px-12 lg:px-8 xl:px-16">
                <div class="flex flex-col items-center justify-between gap-6 pb-6 sm:flex-row">
                    <a href="/" class="block w-max">
                        <img class="h-9 w-auto" src="/favicon.svg" alt="Aria logo" width="120" height="120" />
                    </a>
                    <ul class="group items-center justify-center gap-0 text-center text-sm text-stone-200 sm:flex lg:gap-0">
                        <li class="transition duration-500 group-hover:text-stone-300">
                            <a href="pages/about.html" class="block px-3 py-1">About me</a>
                        </li>
                    </ul>
                </div>
                <div class="flex flex-col flex-wrap items-center justify-between gap-4 py-6 text-sm text-stone-200 sm:flex-row sm:gap-6">
                    <span class="order-last sm:order-first">Copyright &copy; Aria 2019 - Present</span>
                    <ul class="order-first flex gap-6 sm:order-2">
                        <li>
                            <a href="#" target="_blank" class="transition duration-300 hover:text-stone-400">Twitter</a>
                        </li>
                        <li>
                            <a href="#" target="_blank" class="transition duration-300 hover:text-stone-400">Behance</a>
                        </li>
                        <li>
                            <a href="#" target="_blank" class="transition duration-300 hover:text-stone-400">Instagram</a>
                        </li>
                    </ul>
                    <a href="/pages/privacy.html" class="order-2 transition duration-300 hover:text-stone-400 sm:order-last">Privacy Policy</a>
                </div>
            </div>
        </footer>
        
    </body>
</html>
