			<?php 
            $email          = get_field('email', 'option');
            $email_text     = get_field('email_text', 'option');
            $instagram      = get_field('instagram', 'option');
            $linkedin       = get_field('linkedin', 'option');
            ?>
            <section class="py-32">
				<div class="mx-auto max-w-7xl px-4 sm:px-12 lg:px-8 xl:px-16">
					<?php if($email) { ?>
                        <a href="mailto:<?php echo esc_html($email); ?>" class="relative mt-4 block text-center text-5xl font-thin sm:text-7xl">
                            <span class="text-stone-700 transition duration-700 hover:bg-gradient-to-r hover:from-accent hover:to-primary hover:bg-clip-text hover:text-transparent dark:text-white"><?php echo esc_html($email); ?></span>
                        </a>
                    <?php } ?>
					<?php if($email_text) { ?>
                        <div class="mx-auto mt-12 sm:w-4/5 md:w-2/5">
                            <p class="text-center text-lg font-normal text-stone-600 dark:text-stone-300"><?php echo wp_kses_post($email_text); ?></p>
                        </div>
                    <?php } ?>
				</div>
			</section>
		
		</main>

        <?php 
		$footer_class = 'pt-6 bg-stone-50 dark:bg-stone-900';
		if( is_404() || is_privacy_policy() ) {
			$footer_class = 'bg-stone-800 pt-6';
		}
        ?>
		<footer class="<?php echo $footer_class; ?>">
            <div class="mx-auto max-w-7xl divide-y divide-stone-200 dark:divide-white px-4 dark:divide-stone-700 sm:px-12 lg:px-8 xl:px-16">
                <div class="flex flex-col items-center justify-between gap-6 pb-6 sm:flex-row">
                    <a href="<?php echo site_url(); ?>" class="block w-max relative top-1.5">
                        <img class="h-11 w-auto" src="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon.svg" alt="Aria logo" width="120" height="120" />
                    </a>
                    <ul class="group items-center justify-center gap-0 text-center text-sm text-stone-700 dark:text-stone-200 sm:flex lg:gap-0">
                        <li class="transition duration-500 group-hover:text-stone-300">
                            <a href="<?php echo site_url(); ?>" class="block px-3 py-1">Portfolio</a>
                        </li>
                        <li class="transition duration-500 group-hover:text-stone-300">
                            <a href="<?php echo site_url('/cenik'); ?>" class="block px-3 py-1">Ceník</a>
                        </li>
                        <li class="transition duration-500 group-hover:text-stone-300">
                            <a href="<?php echo site_url('/o-mne'); ?>" class="block px-3 py-1">O mně</a>
                        </li>
                    </ul>
                </div>
                <div class="flex flex-col flex-wrap items-center justify-between gap-4 py-6 text-sm text-stone-700 dark:text-stone-200 sm:flex-row sm:gap-6">
                    <span class="order-last sm:order-first">&copy; <?php echo wp_date('Y'); ?> - Yvica Photography</span>
                    <ul class="order-first flex gap-6 sm:order-2">
                        <?php if($instagram) { ?>
                            <li>
                                <a href="<?php echo esc_url($instagram); ?>" target="_blank" class="transition duration-300 hover:text-stone-400">Instagram</a>
                            </li>
                        <?php } ?>
                        <?php if($linkedin) { ?>
                            <li>
                                <a href="<?php echo esc_url($linkedin); ?>" target="_blank" class="transition duration-300 hover:text-stone-400">LinkedIn</a>
                            </li>
                        <?php } ?>
                    </ul>
                    <a href="<?php echo site_url('/ochrana-osobnich-udaju'); ?>" class="order-2 transition duration-300 hover:text-stone-400 sm:order-last">Podmínky ochrany osobních údajů</a>
                </div>
            </div>
        </footer>

		<?php wp_footer(); ?>
    </body>
</html>
