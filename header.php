<!doctype html>
<html <?php language_attributes(); ?>>
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        
		<?php wp_head(); ?>
    </head>

	<?php 
	$body_class = 'bg-white dark:bg-stone-950';
	if( is_404() || is_privacy_policy() ) {
		$body_class = 'bg-stone-50 dark:bg-stone-950';
	}
	?>
    <body <?php body_class($body_class); ?>>
        <header class="py-6 sm:py-12 xl:py-24">
            <div class="mx-auto max-w-5xl px-8 sm:px-12 lg:px-8 xl:px-16">
                <div class="flex flex-wrap justify-between lg:items-center">
                    <div class="relative z-30 flex w-full items-center justify-between lg:w-auto">
                        <a href="<?php echo site_url(); ?>" class="relative top-1.5">
                            <img class="h-14 w-auto" src="<?php echo get_stylesheet_directory_uri(); ?>/dist/favicon.svg" alt="Yvica Photography logo" width="120" height="120" />
                        </a>
                        <button aria-label="humburger" id="hamburger" class="-mr-6 p-6 lg:hidden">
                            <div aria-hidden="true" class="m-auto h-0.5 w-6 rounded bg-primary transition duration-300"></div>
                            <div aria-hidden="true" class="m-auto mt-2 h-0.5 w-6 rounded bg-primary transition duration-300"></div>
                        </button>
                    </div>
                    <?php 
                    $email          = get_field('email', 'option');
                    $instagram      = get_field('instagram', 'option');
                    $linkedin       = get_field('linkedin', 'option');
                    ?>
                    <div id="navlinks" class="fixed inset-0 z-20 hidden h-full w-full items-center justify-center bg-white/90 backdrop-blur-3xl lg:static lg:block lg:h-auto lg:w-auto lg:bg-transparent lg:backdrop-blur-none">
                        <ul class="group flex flex-col items-center gap-6 text-lg text-stone-600 dark:text-stone-300 lg:flex-row lg:gap-0 lg:text-base">
                            <li class="text-right transition duration-500 group-hover:text-stone-300 lg:text-left">
                                <a aria-current="page" href="<?php echo site_url('/cenik'); ?>" class="block px-6 py-1 hover:text-primary">Ceník</a>
                            </li>
                            <li class="text-right transition duration-500 group-hover:text-stone-300 lg:text-left">
                                <a aria-current="page" href="<?php echo site_url('/o-mne'); ?>" class="block px-6 py-1 hover:text-primary">O mně</a>
                            </li>
                            <?php if($instagram) { ?>
                                <li class="text-right transition duration-500 group-hover:text-stone-300 lg:text-left">
                                    <a href="<?php echo esc_url($instagram); ?>" target="_blank" class="block px-6 py-1 hover:text-primary">Instagram</a>
                                </li>
                            <?php } ?>
                            <?php if($linkedin) { ?>
                                <li class="text-right transition duration-500 group-hover:text-stone-300 lg:text-left">
                                    <a href="<?php echo esc_url($linkedin); ?>" target="_blank" class="block px-6 py-1 hover:text-primary">LinkedIn</a>
                                </li>
                            <?php } ?>
                            <?php if($email) { ?>
                            <li class="text-right group-hover:text-stone-300 lg:pl-6 lg:text-left">
                                <a href="mailto:<?php echo esc_html($email); ?>" class="flex h-9 items-center overflow-hidden border border-stone-300 px-3.5 py-1 text-base transition-all duration-300 hover:border-primary hover:bg-primary hover:text-white">
                                    <span class="relative flex items-center">
                                        <?php echo esc_html($email); ?>
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-4 w-4 translate-x-1 duration-300 group-hover:translate-x-2.5">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                                        </svg>
                                    </span>
                                </a>
                            </li>
                            <?php } ?>
                        </ul>
                    </div>
                </div>
            </div>
        </header>

        <main>