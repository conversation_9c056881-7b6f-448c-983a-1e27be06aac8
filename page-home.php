<?php
/**
 * Template Name: Home
 */

get_header();
?>

<section class="pt-24 sm:pt-32 lg:pt-20">
    
    <?php 
    $email          = get_field('email', 'option');
    $instagram      = get_field('instagram', 'option');
    $slogan         = get_field('slogan', 'option');
    $slogan_text    = get_field('slogan_text', 'option');
    ?>
    <div data-rellax-speed="-2" class="rellax mx-auto max-w-5xl px-8 sm:px-12 lg:px-8 xl:px-16">
        <div class="sm:w-4/5">
            <h1 class="text-3xl font-semibold text-stone-900 dark:text-white sm:text-4xl lg:text-5xl lg:leading-snug"><?php echo wp_kses_post($slogan); ?></h1>
            <p class="mt-4 text-xl text-stone-600 dark:text-stone-300"><?php echo wp_kses_post($slogan_text); ?></p>
        </div>
    </div>

    <?php 
    $projects = get_posts(array(
        'post_type'         => 'post',
        'posts_per_page'    => -1,
        'orderby'           => 'date',
        'order'             => 'DESC',
        'fields'            => 'ids'
    ));
    if($projects) {
    ?>
    <div class="relative mt-14 w-full bg-white px-4 pt-12 dark:bg-stone-950 sm:px-12 lg:px-8">
        <div class="mx-auto grid max-w-7xl gap-4 gap-y-20 sm:grid-cols-5 sm:gap-y-24 md:gap-8 lg:gap-16 lg:gap-y-16">
            <?php 
            $i = 0;
            foreach($projects as $project) { 
                $i++;
                $title = get_the_title($project);
                $cover_id = get_post_thumbnail_id($project);
                $url = get_permalink($project);
                $cover = ($cover_id) ? wp_get_attachment_image_src($cover_id, 'large') : false;
                $cover_url = ($cover) ? $cover[0] : false;
                $cover_width = ($cover) ? $cover[1] : false;
                $cover_height = ($cover) ? $cover[2] : false;
                if($i % 5 === 1) {
                ?>
                <a href="<?php echo esc_url($url); ?>" class="group sm:col-span-full lg:col-span-3">
                    <?php if($cover) { ?>
                        <div class="relative overflow-hidden before:absolute before:inset-0 before:z-[1] before:origin-right before:scale-x-0 before:bg-gradient-to-r before:from-transparent before:via-surface/20 before:to-transparent before:transition before:duration-500 group-hover:before:origin-left group-hover:before:scale-x-100">
                            <img class="w-full object-cover" src="<?php echo esc_url($cover_url); ?>" alt="project cover" width="<?php echo esc_attr($cover_width); ?>" height="<?php echo esc_attr($cover_height); ?>" loading="lazy" />
                        </div>
                    <?php } ?>
                    <div class="flex items-center justify-between py-4 text-stone-700 dark:text-stone-100">
                        <h2 class="text-lg font-medium duration-300 group-hover:text-primary"><?php echo esc_html($title); ?></h2>
                        <span class="text-sm"><?php echo '/ ' . str_pad($i, 2, '0', STR_PAD_LEFT); ?></span>
                    </div>
                </a>
                <?php } elseif($i % 5 === 2) { ?>
                <a href="<?php echo esc_url($url); ?>" class="group sm:col-span-4 sm:col-start-2 md:pt-8 lg:col-span-2 lg:pt-16">
                    <?php if($cover) { ?>
                        <div class="relative before:absolute before:inset-0 before:origin-right before:scale-x-0 before:bg-gradient-to-r before:from-transparent before:via-surface/20 before:to-transparent before:transition before:duration-500 group-hover:before:origin-left group-hover:before:scale-x-100">
                            <img class="w-full object-cover" src="<?php echo esc_url($cover_url); ?>" alt="project cover" width="<?php echo esc_attr($cover_width); ?>" height="<?php echo esc_attr($cover_height); ?>" loading="lazy" />
                        </div>
                    <?php } ?>
                    <div class="flex items-center justify-between py-4 text-stone-700 dark:text-stone-100">
                        <h2 class="text-lg font-medium duration-300 group-hover:text-primary"><?php echo esc_html($title); ?></h2>
                        <span class="text-sm"><?php echo '/ ' . str_pad($i, 2, '0', STR_PAD_LEFT); ?></span>
                    </div>
                </a>
                <?php } elseif($i % 5 === 3) { ?>
                <a href="<?php echo esc_url($url); ?>" class="group sm:col-span-3 sm:col-start-3 lg:col-span-2">
                    <?php if($cover) { ?>
                        <div class="relative before:absolute before:inset-0 before:origin-right before:scale-x-0 before:bg-gradient-to-r before:from-transparent before:via-surface/20 before:to-transparent before:transition before:duration-500 group-hover:before:origin-left group-hover:before:scale-x-100">
                            <img class="w-full object-cover" src="<?php echo esc_url($cover_url); ?>" alt="project cover" width="<?php echo esc_attr($cover_width); ?>" height="<?php echo esc_attr($cover_height); ?>" loading="lazy" />
                        </div>
                    <?php } ?>
                    <div class="flex items-center justify-between py-4 text-stone-700 dark:text-stone-100">
                        <h2 class="text-lg font-medium duration-300 group-hover:text-primary"><?php echo esc_html($title); ?></h2>
                        <span class="text-sm"><?php echo '/ ' . str_pad($i, 2, '0', STR_PAD_LEFT); ?></span>
                    </div>
                </a>
                <?php } elseif($i % 5 === 4) { ?>
                <a href="<?php echo esc_url($url); ?>" class="group sm:col-span-4 lg:col-span-3 lg:mt-16">
                    <?php if($cover) { ?>
                        <div class="relative before:absolute before:inset-0 before:origin-right before:scale-x-0 before:bg-gradient-to-r before:from-transparent before:via-surface/20 before:to-transparent before:transition before:duration-500 group-hover:before:origin-left group-hover:before:scale-x-100">
                            <img class="w-full" src="<?php echo esc_url($cover_url); ?>" alt="project cover" width="<?php echo esc_attr($cover_width); ?>" height="<?php echo esc_attr($cover_height); ?>" loading="lazy" />
                        </div>
                    <?php } ?>
                    <div class="flex items-center justify-between py-4 text-stone-700 dark:text-stone-100">
                        <h2 class="text-lg font-medium duration-300 group-hover:text-primary"><?php echo esc_html($title); ?></h2>
                        <span class="text-sm"><?php echo '/ ' . str_pad($i, 2, '0', STR_PAD_LEFT); ?></span>
                    </div>
                </a>
                <?php } elseif($i % 5 === 0) { ?>
                <a href="<?php echo esc_url($url); ?>" class="group gap-6 sm:col-span-full lg:mt-16 lg:flex lg:items-end">
                    <?php if($cover) { ?>
                        <div class="relative before:absolute before:inset-0 before:origin-right before:scale-x-0 before:bg-gradient-to-r before:from-transparent before:via-surface/20 before:to-transparent before:transition before:duration-500 group-hover:before:origin-left group-hover:before:scale-x-100 lg:w-4/5">
                            <img class="w-full" src="<?php echo esc_url($cover_url); ?>" alt="project cover" width="<?php echo esc_attr($cover_width); ?>" height="<?php echo esc_attr($cover_height); ?>" loading="lazy" />
                        </div>
                    <?php } ?>
                    <div class="flex justify-between py-4 text-stone-700 dark:text-stone-100 lg:block lg:space-y-4">
                       <span class="text-sm"><?php echo '/ ' . str_pad($i, 2, '0', STR_PAD_LEFT); ?></span>
                        <h2 class="order-first text-lg font-medium duration-300 group-hover:text-primary"><?php echo esc_html($title); ?></h2>
                    </div>
                </a>
                <?php } ?>
            <?php } ?>
        </div>
    </div>
    <?php } ?>

</section>

<?php
get_footer();