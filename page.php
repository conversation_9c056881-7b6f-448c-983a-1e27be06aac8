<?php
get_header();
?>

<section class="page-section-before pt-24 sm:pt-32 lg:pt-20">
    <div class="mx-auto max-w-5xl px-8 sm:px-12 lg:px-8 xl:px-16">
        <h1 class="text-center text-3xl font-semibold text-stone-900 dark:text-white sm:text-4xl lg:text-5xl lg:leading-snug"><?php the_title(); ?></h1>
        <div class="mt-4 text-center text-xl text-stone-600 dark:text-stone-300"><?php the_content(); ?></div>
        <?php 
		$cover_id = get_post_thumbnail_id(get_the_ID());
		$cover = ($cover_id) ? wp_get_attachment_image_src($cover_id, 'full') : false;
		$cover_alt = get_post_meta($cover_id, '_wp_attachment_image_alt', true);
		$cover_url = ($cover) ? $cover[0] : false;
		$cover_width = ($cover) ? $cover[1] : false;
		$cover_height = ($cover) ? $cover[2] : false;
		if($cover) {
		?>
		<div class="mt-20 h-full overflow-hidden sm:h-96 md:h-[40rem]">
            <img data-rellax-speed="3" data-rellax-xs-speed="0" class="rellax h-max w-full" src="<?php echo esc_url($cover_url); ?>" alt="<?php echo esc_attr($cover_alt); ?>" width="<?php echo esc_attr($cover_width); ?>" height="<?php echo esc_attr($cover_height); ?>" />
        </div>
		<?php 
		}
		
		if( have_rows('bloky') ) {
			while ( have_rows('bloky') ) { the_row();

				if( get_row_layout() == 'heading_and_text' ) {
					$nahore = get_sub_field('nahore');
					if ($nahore) {
						$nadpis = get_sub_field('nadpis');
						$text = get_sub_field('text');
						if(!$nadpis && !$text) {
							continue;
						}
						?>
						<div class="my-20 grid gap-6 sm:my-32 md:my-40 md:grid-cols-2 md:gap-12">
							<div>
								<h2 class="text-4xl font-bold text-stone-800 dark:text-white"><?php echo wp_kses_post($nadpis); ?></h2>
							</div>
							<div>
								<div class="mt-6 text-stone-700 dark:text-stone-300 sm:mt-0">
									<div class="text-lg"><?php echo wp_kses_post($text); ?></div>
								</div>
							</div>
						</div>
						<?php 
					}
				} elseif( get_row_layout() == 'text_twice' ) {
					$nahore = get_sub_field('nahore');
					if ($nahore) {
						$text_vlevo = get_sub_field('text_vlevo');
						$text_vpravo = get_sub_field('text_vpravo');
						if(!$text_vlevo && !$text_vpravo) {
							continue;
						}
						?>
						<div class="mt-12 grid gap-6 sm:grid-cols-2 md:mt-20 md:gap-12">
							<div class="text-lg text-stone-700 dark:text-stone-300"><?php echo wp_kses_post($text_vlevo); ?></div>
							<div class="text-lg text-stone-700 dark:text-stone-300"><?php echo wp_kses_post($text_vpravo); ?></div>
						</div>
					<?php 
					}
				}

			}
		}

		?>
    </div>
</section>

<?php 
if( have_rows('bloky') ) {
	while ( have_rows('bloky') ) { the_row();

		if( get_row_layout() == 'mosaic' ) {
			$fotografie = get_sub_field('fotografie');
			$foto_nadpis = get_sub_field('foto_nadpis');
			$foto_popis = get_sub_field('foto_popis');
			if(!$fotografie) {
				continue;
			}
			?>
			<section class="page-section px-1 sm:px-12">
				<div class="mx-auto grid grid-cols-10 place-content-center place-items-center gap-1 sm:gap-2 lg:w-4/5">
					<div class="col-span-3">
						<?php if($fotografie[0]) { ?>
							<img class="ml-auto w-4/5" src="<?php echo esc_url($fotografie[0]['url']); ?>" alt="<?php echo esc_attr($fotografie[0]['alt']); ?>" width="<?php echo esc_attr($fotografie[0]['width']); ?>" height="<?php echo esc_attr($fotografie[0]['height']); ?>" />
						<?php } ?>
						<div class="mt-1 grid grid-cols-2 gap-1 sm:mt-2 sm:gap-2">
							<?php if($fotografie[1]) { ?>
								<img src="<?php echo esc_url($fotografie[1]['url']); ?>" alt="<?php echo esc_attr($fotografie[1]['alt']); ?>" width="<?php echo esc_attr($fotografie[1]['width']); ?>" height="<?php echo esc_attr($fotografie[1]['height']); ?>" />
							<?php } ?>
							<?php if($fotografie[2]) { ?>
								<img src="<?php echo esc_url($fotografie[2]['url']); ?>" alt="<?php echo esc_attr($fotografie[2]['alt']); ?>" width="<?php echo esc_attr($fotografie[2]['width']); ?>" height="<?php echo esc_attr($fotografie[2]['height']); ?>" />
							<?php } ?>
						</div>
					</div>
					<div class="group col-span-4 overflow-hidden">
						<div class="relative">
							<?php if($foto_nadpis || $foto_popis) { ?>
								<div class="absolute inset-0 z-[1] flex transition duration-500 group-hover:bg-stone-950/60">
									<div class="mt-auto h-max p-4 text-white">
										<div class="overflow-hidden">
											<h3 class="translate-y-8 rotate-12 text-lg transition-all duration-500 group-hover:translate-y-0 group-hover:rotate-0"><?php echo esc_html($foto_nadpis); ?></h3>
										</div>
										<div class="overflow-hidden">
											<p class="translate-y-6 text-sm tracking-wide transition-all delay-300 duration-500 group-hover:translate-y-0"><?php echo esc_html($foto_popis); ?></p>
										</div>
									</div>
								</div>
							<?php } ?>
							<img class="transition duration-700 group-hover:scale-105" src="<?php echo esc_url($fotografie[3]['url']); ?>" alt="<?php echo esc_attr($fotografie[3]['alt']); ?>" />
						</div>
					</div>
					<div class="col-span-3">
						<?php if($fotografie[4]) { ?>
							<img class="w-4/5" src="<?php echo esc_url($fotografie[4]['url']); ?>" alt="<?php echo esc_attr($fotografie[4]['alt']); ?>" width="<?php echo esc_attr($fotografie[4]['width']); ?>" height="<?php echo esc_attr($fotografie[4]['height']); ?>" />
						<?php } ?>
						<div class="mt-1 grid grid-cols-2 gap-1 sm:mt-2 sm:gap-2">
							<?php if($fotografie[5]) { ?>
								<img src="<?php echo esc_url($fotografie[5]['url']); ?>" alt="<?php echo esc_attr($fotografie[5]['alt']); ?>" width="<?php echo esc_attr($fotografie[5]['width']); ?>" height="<?php echo esc_attr($fotografie[5]['height']); ?>" />
							<?php } ?>
							<?php if($fotografie[6]) { ?>
								<img src="<?php echo esc_url($fotografie[6]['url']); ?>" alt="<?php echo esc_attr($fotografie[6]['alt']); ?>" width="<?php echo esc_attr($fotografie[6]['width']); ?>" height="<?php echo esc_attr($fotografie[6]['height']); ?>" />
							<?php } ?>
						</div>
					</div>
				</div>
			</section>
			<?php
		} elseif( get_row_layout() == 'pricing' ) {
			if( have_rows('plany') ) {
				?>
				<section class="page-section py-24 sm:py-32 lg:py-20 px-1 sm:px-12">
					<div class="mx-auto max-w-7xl px-4 sm:px-12 lg:px-8 xl:px-16">
						<div class="flex w-full flex-col justify-center items-center text-base h-100vh mx-auto">
							<div class="isolate w-full mx-auto grid max-w-md grid-cols-1 gap-6 sm:gap-8 lg:gap-0 xl:gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-3">
								<?php 
								while( have_rows('plany') ) { 
									the_row();
									$nazev = get_sub_field('nazev');
									$popis = get_sub_field('popis');
									$cena = get_sub_field('cena');
									$tlacitko = get_sub_field('tlacitko');
									?>
									<div class="ring-1 ring-stone-200 dark:ring-stone-500 p-6 sm:p-8 xl:p-10">
										<div class="flex items-center gap-x-3">
											<h3 class="text-stone-900 dark:text-white text-xl sm:text-2xl font-bold leading-8"><?php echo esc_html($nazev); ?></h3>
										</div>
										<div class="mt-4 text-base leading-6 text-stone-600 dark:text-stone-300"><?php echo wp_kses_post($popis); ?></div>
										<p class="mt-2 sm:mt-6 flex items-baseline gap-x-1">
											<span class="text-4xl sm:text-5xl font-bold tracking-tight text-stone-900 dark:text-white"><?php echo esc_html($cena); ?></span>
										</p>
										<?php if($tlacitko) { ?>
											<a href="<?php echo esc_url($tlacitko['url']); ?>" class="text-stone-600 dark:text-white ring-1 ring-inset ring-[#765d69]/50 dark:ring-[#765d69] hover:bg-[#765d69] hover:text-white mt-2 sm:mt-6 block rounded-md py-3 px-3 text-center text-base font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-stone-600" target="<?php echo esc_attr($tlacitko['target']); ?>"><?php echo esc_html($tlacitko['title']); ?></a>
										<?php } ?>
										<?php if( have_rows('polozky') ) { ?>
											<ul role="list" class="mt-8 space-y-2 sm:space-y-2.5 text-sm leading-6 text-stone-600 dark:text-stone-300 xl:mt-10">
												<?php while( have_rows('polozky') ) { 
													the_row();
													$polozka = get_sub_field('polozka');
													if(!$polozka) {
														continue;
													}
													?>
													<li class="flex gap-x-3 text-base">
														<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" class="h-6 w-5 flex-none text-stone-600 dark:text-stone-400">
															<path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
														</svg><?php echo esc_html($polozka); ?>
													</li>
												<?php } ?>
											</ul>
										<?php } ?>
									</div>
								<?php } ?>
							</div>
						</div>
					</div>
				</section>
			<?php	
			}	
		} elseif( get_row_layout() == 'cta' ) {
			$nadpis = get_sub_field('nadpis');
			$text = get_sub_field('text');
			$tlacitko = get_sub_field('tlacitko');
			?>
			<section class="pt-0 sm:pt-8 pb-24 sm:pb-32 lg:pb-28">
                <div class="mx-auto max-w-5xl px-8 sm:px-12 lg:px-8 xl:px-16">
                    <?php if($nadpis) { ?>
						<h3 class="text-2xl font-semibold text-stone-900 dark:text-white sm:text-3xl text-center"><?php echo esc_html($nadpis); ?></h3>
					<?php } ?>
					<?php if($text) { ?>
                    	<div class="mt-4 text-center text-xl text-stone-600 dark:text-stone-300 leading-8"><?php echo wp_kses_post($text); ?></div>
					<?php } ?>
                    <?php if($tlacitko) { ?>
						<div class="flex justify-center">
							<a href="<?php echo esc_url($tlacitko['url']); ?>" target="<?php echo esc_url($tlacitko['target']); ?>" class="bg-[#765d69] text-white shadow-sm hover:bg-[#664d59] mt-6 inline-block rounded-md py-3 px-7 text-center text-base font-semibold leading-6 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-stone-600" target="_blank"><?php echo esc_html($tlacitko['title']); ?></a>
						</div>
					<?php } ?>
                </div>
            </section>
			<?php
		}

	}
}
?>

<section class="page-section-after">
    <div class="mx-auto max-w-5xl px-8 sm:px-12 lg:px-8 xl:px-16">
		<?php 
		if( have_rows('bloky') ) {
			while ( have_rows('bloky') ) { the_row();

				if( get_row_layout() == 'heading_and_text' ) {
					$nahore = get_sub_field('nahore');
					if (!$nahore) {
						?>
						<section>
							<div class="mx-auto max-w-5xl px-8 sm:px-12 lg:px-8 xl:px-16">
								<?php 
								$nadpis = get_sub_field('nadpis');
								$text = get_sub_field('text');
								if(!$nadpis && !$text) {
									continue;
								}
								?>
								<div class="my-20 grid gap-6 md:grid-cols-2 md:gap-12">
									<div>
										<h2 class="text-4xl font-bold text-stone-800 dark:text-white"><?php echo wp_kses_post($nadpis); ?></h2>
									</div>
									<div>
										<div class="mt-6 text-stone-700 dark:text-stone-300 sm:mt-0">
											<div class="text-lg"><?php echo wp_kses_post($text); ?></div>
										</div>
									</div>
								</div>
							</div>
						</section>
						<?php
					}
				} elseif( get_row_layout() == 'text_twice' ) {
					$nahore = get_sub_field('nahore');
					if (!$nahore) {
					?>
					<section>
						<div class="mx-auto max-w-5xl px-8 sm:px-12 lg:px-8 xl:px-16">
							<?php 
							$text_vlevo = get_sub_field('text_vlevo');
							$text_vpravo = get_sub_field('text_vpravo');
							if(!$text_vlevo && !$text_vpravo) {
								continue;
							}
							?>
							<div class="mt-12 grid gap-6 sm:grid-cols-2 md:mt-20 md:gap-12">
								<div class="text-lg text-stone-700 dark:text-stone-300"><?php echo wp_kses_post($text_vlevo); ?></div>
								<div class="text-lg text-stone-700 dark:text-stone-300"><?php echo wp_kses_post($text_vpravo); ?></div>
							</div>
						</div>
					</section>
					<?php
					}
				}

			}
		}

		?>
    </div>
</section>

<?php
get_footer();
