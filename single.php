<?php
get_header();
?>

<?php while(have_posts()) { 
	the_post();
	?>
	<section class="pt-12 sm:pt-20 md:pt-20">
		<div class="mx-auto max-w-5xl px-8 sm:px-12 lg:px-8 xl:px-16">
			<div class="lg:w-4/5">
				<h1 class="text-3xl font-semibold text-stone-950 dark:text-white sm:text-5xl"><?php the_title(); ?></h1>
				<div class="text-lg ml-auto mt-12 text-stone-700 dark:text-stone-300 sm:w-3/5 lg:w-1/2"><?php the_content(); ?></div>
			</div>
		</div>
	</section>

	<section class="mt-24 lg:mt-32 space-y-2 sm:space-y-4">

		<?php 
		if( have_rows('bloky') ) {
			while ( have_rows('bloky') ) { the_row();

				if( get_row_layout() == 'heading_and_text' ) {
					$nadpis = get_sub_field('nadpis');
					$text = get_sub_field('text');
					if( !$nadpis && !$text ) {
						continue;
					}
					?>
					<div class="mx-auto max-w-5xl px-8 sm:px-12 lg:px-8 xl:px-16">
						<div class="gap-4 sm:flex sm:gap-8 lg:w-4/5 lg:gap-12">
							<h2 class="text-2xl font-semibold text-stone-900 dark:text-white sm:w-2/5 sm:text-4xl"><?php echo wp_kses_post($nadpis); ?></h2>
							<div class="text-lg mt-6 text-stone-700 dark:text-stone-300 sm:mt-0 sm:w-3/5"><?php echo wp_kses_post($text); ?></div>
						</div>
					</div>
					<?php 
				} elseif( get_row_layout() == 'text' ) {
					$zobrazeni = get_sub_field('zobrazeni');
					$text = get_sub_field('text');
					$class = match ($zobrazeni) {
						'full' => 'mt-6 text-lg text-stone-700 dark:text-stone-300 sm:mt-0',
						'left' => 'mt-6 text-lg text-stone-700 dark:text-stone-300 sm:mt-0 sm:w-1/2',
						'right' => 'mt-6 text-lg text-stone-700 dark:text-stone-300 sm:mt-0 col-start-2 sm:w-1/2 ml-auto',
					};
					if( !$text ) {
						continue;
					}
					?>
					<div class="mx-auto max-w-5xl px-8 sm:px-12 lg:px-8 xl:px-16">
						<div class="my-12 grid gap-6 <?php if($zobrazeni !== 'full') { echo 'sm:grid-cols-2'; } ?> md:my-20 md:gap-12">
							<div class="<?php echo $class; ?>"><?php echo wp_kses_post($text); ?></div>
						</div>
					</div>
					<?php 
				} elseif( get_row_layout() == 'text_twice' ) {
					$text_left = get_sub_field('text_left');
					$text_right = get_sub_field('text_right');
					if( !$text_left && !$text_right ) {
						continue;
					}
					?>
					<div class="mx-auto max-w-5xl px-8 sm:px-12 lg:px-8 xl:px-16">
						<div class="my-12 grid gap-6 sm:grid-cols-2 md:my-20 md:gap-12">
							<div class="text-lg text-stone-700 dark:text-stone-300"><?php echo wp_kses_post($text_left); ?></div>
							<div class="text-lg text-stone-700 dark:text-stone-300"><?php echo wp_kses_post($text_right); ?></div>
						</div>
					</div>
					<?php 
				} elseif( get_row_layout() == 'photos_two_thirds' ) {
					$photo_left = get_sub_field('photo_left');
					$photo_right = get_sub_field('photo_right');
					if( !$photo_left && !$photo_right ) {
						continue;
					}
					?>
					<div class="mx-auto max-w-7xl px-4 sm:px-12 lg:px-8">
						<div class="group grid gap-2 sm:grid-cols-3 sm:gap-4">
							<img class="h-full w-full object-cover sm:col-span-2" src="<?php echo $photo_left['sizes']['large']; ?>" alt="<?php echo $photo_left['alt']; ?>" width="<?php echo $photo_left['sizes']['large-width']; ?>" height="<?php echo $photo_left['sizes']['large-height']; ?>" loading="lazy" />
							<img class="h-full w-full object-cover object-left" src="<?php echo $photo_right['sizes']['large']; ?>" alt="<?php echo $photo_right['alt']; ?>" width="<?php echo $photo_right['sizes']['large-width']; ?>" height="<?php echo $photo_right['sizes']['large-height']; ?>" loading="lazy"  />
						</div>
					</div>
					<?php 
				} elseif( get_row_layout() == 'photos_one_half' ) {
					$photo_left = get_sub_field('photo_left');
					$photo_right = get_sub_field('photo_right');
					if( !$photo_left && !$photo_right ) {
						continue;
					}
					?>
					<div class="mx-auto max-w-7xl px-4 sm:px-12 lg:px-8">
						<div class="grid grid-cols-2 gap-2 sm:gap-4">
							<img class="h-full w-full object-cover" src="<?php echo $photo_left['sizes']['large']; ?>" alt="<?php echo $photo_left['alt']; ?>" width="<?php echo $photo_left['sizes']['large-width']; ?>" height="<?php echo $photo_left['sizes']['large-height']; ?>" loading="lazy" />
							<img class="h-full w-full object-cover" src="<?php echo $photo_right['sizes']['large']; ?>" alt="<?php echo $photo_right['alt']; ?>" width="<?php echo $photo_right['sizes']['large-width']; ?>" height="<?php echo $photo_right['sizes']['large-height']; ?>" loading="lazy" />
						</div>
					</div>
					<?php 
				} elseif( get_row_layout() == 'photo' ) {
					$photos = get_sub_field('photos');
					if( !$photos ) {
						continue;
					}
					?>
					<div class="mx-auto max-w-7xl px-4 sm:px-12 lg:px-8">
						<div class="space-y-2 sm:space-y-4">
							<?php foreach($photos as $photo) { ?>
								<img class="h-full w-full object-cover" src="<?php echo $photo['sizes']['large']; ?>" alt="<?php echo $photo['alt']; ?>" width="<?php echo $photo['sizes']['large-width']; ?>" height="<?php echo $photo['sizes']['large-height']; ?>" loading="lazy" />
							<?php } ?>
						</div>
					</div>
					<?php
				}

			}
		}
		?>
	</section>
<?php } ?>

<?php
get_footer();
